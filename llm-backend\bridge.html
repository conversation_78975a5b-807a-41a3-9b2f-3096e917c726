<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>websocket</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
        }
        
        .container {
            width: 100%;
            max-width: 800px;
            margin-top: 40px;
            position: relative;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 10px;
            background-color: #ff6b6b;
            vertical-align: middle;
        }
        
        .status-indicator.connecting {
            background-color: #ffd43b;
            animation: bounceFade 1.5s infinite ease-in-out;
        }
        
        .status-indicator.active {
            background-color: #51cf66;
            animation: bounceFade 1.2s infinite ease-in-out;
        }
        
        @keyframes bounceFade {
            0% { 
                opacity: 1; 
                transform: scale(1);
            }
            50% { 
                opacity: 0; 
                transform: scale(0.8);
            }
            70% { 
                opacity: 0.3; 
                transform: scale(1.1);
            }
            100% { 
                opacity: 1; 
                transform: scale(1);
            }
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .log-header {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 20px;
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-header h3 {
            margin: 0;
            color: #ffffff;
            font-size: 1.1rem;
            font-weight: 500;
        }
        
        .log-content {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #e1e1e1;
            background: transparent;
            padding: 20px;
            height: 180px;
            max-height: 180px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
            border-radius: 0 0 12px 12px;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE/Edge */
        }
        
        .log-content::-webkit-scrollbar {
            display: none; /* Chrome/Safari/Opera */
        }
        
        .log-fade-top {
            position: absolute;
            top: 55px;
            left: 0;
            right: 0;
            height: 80px;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), transparent);
            pointer-events: none;
            z-index: 1;
            border-radius: 0 0 12px 12px;
        }
        
        .log-content::-webkit-scrollbar {
            width: 8px;
        }
        
        .log-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }
        
        .log-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        
        .log-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .log-entry {
            margin-bottom: 8px;
        }
        
        .log-entry.info {
            color: #74c0fc;
        }
        
        .log-entry.success {
            color: #51cf66;
        }
        
        .log-entry.error {
            color: #ff6b6b;
        }
        
        .log-entry.warning {
            color: #ffd43b;
        }
        
        .timestamp {
            color: #868e96;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">

        
        <div class="log-container">
            <div class="log-header">
                <h3>server logs<span class="status-indicator" id="statusIndicator"></span></h3>
            </div>
            <div class="log-content" id="logContent"></div>
            <div class="log-fade-top"></div>
        </div>
    </div>

    <script src="https://js.puter.com/v2/"></script>
    <script>
        const logContent = document.getElementById('logContent');
        const statusIndicator = document.getElementById('statusIndicator');
        let hasReceivedCommand = false;
        let isAutoScrolling = true;
        
        // Check if user is at the bottom of the scroll
        function isAtBottom() {
            return logContent.scrollTop >= (logContent.scrollHeight - logContent.clientHeight - 5);
        }
        
        // Handle scroll events to detect manual scrolling
        logContent.addEventListener('scroll', function() {
            if (isAtBottom()) {
                isAutoScrolling = true;
            } else {
                isAutoScrolling = false;
            }
        });
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span>${message}`;
            
            logContent.appendChild(logEntry);
            
            // Only auto-scroll if user hasn't manually scrolled up
            if (isAutoScrolling) {
                logContent.scrollTop = logContent.scrollHeight;
            }
        }
        
        function updateStatus(state) {
            statusIndicator.className = 'status-indicator';
            if (state === 'connecting') {
                statusIndicator.classList.add('connecting');
            } else if (state === 'active') {
                statusIndicator.classList.add('active');
            }
            // Default state (disconnected) has no additional classes - solid red
        }
        
        const ws = new WebSocket('ws://localhost:8765');
        
        addLog('Initializing WebSocket connection...', 'info');
        
        ws.onopen = function() {
            addLog('Connected to Python server', 'success');
            updateStatus('connecting'); // Yellow blinking - connected but no commands yet
        };
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            addLog(`Received message: ${data.type}`, 'info');
            
            if (data.type === 'execute_js') {
                if (!hasReceivedCommand) {
                    hasReceivedCommand = true;
                    updateStatus('active'); // Green blinking - actively processing commands
                }
                
                addLog(`Executing JavaScript code (ID: ${data.id})`, 'warning');
                try {
                    const result = eval(data.code);
                    ws.send(JSON.stringify({
                        type: 'js_result',
                        id: data.id,
                        success: true,
                        result: result
                    }));
                    addLog(`JavaScript executed successfully (ID: ${data.id})`, 'success');
                } catch (error) {
                    ws.send(JSON.stringify({
                        type: 'js_result',
                        id: data.id,
                        success: false,
                        error: error.message
                    }));
                    addLog(`JavaScript execution failed (ID: ${data.id}): ${error.message}`, 'error');
                }
            }
        };
        
        ws.onclose = function() {
            addLog('Disconnected from Python server', 'error');
            updateStatus('disconnected'); // Solid red
            hasReceivedCommand = false; // Reset for next connection
        };
        
        ws.onerror = function(error) {
            addLog('WebSocket error occurred', 'error');
            updateStatus('disconnected'); // Solid red
        };
    </script>
</body>
</html>