#!/usr/bin/env python3
"""
Interactive CLI chat interface for Puter AI
Uses claude-sonnet-4 model with streaming responses
Type 'exit' to quit
"""

from puter import a_bridge
import time
import sys

try:
    from clrprint import clrinput, clrprint
    HAS_CLRPRINT = True
except ImportError:
    HAS_CLRPRINT = False
    def clrinput(prompt, clr=None):
        return input(prompt)
    def clrprint(text, clr=None):
        print(text)

def print_welcome():
    """Print welcome message"""
    if HAS_CLRPRINT:
        clrprint("=" * 60, clr='c')
        clrprint("🤖 Puter AI Chat Interface", clr='y')
        clrprint("Model: claude-sonnet-4", clr='g')
        clrprint("Type 'exit' to quit", clr='b')
        clrprint("=" * 60, clr='c')
    else:
        print("=" * 60)
        print("🤖 Puter AI Chat Interface")
        print("Model: claude-sonnet-4")
        print("Type 'exit' to quit")
        print("=" * 60)

def print_streaming_response(bridge, prompt):
    """Print streaming response from AI"""
    try:
        if HAS_CLRPRINT:
            clrprint("\n🤖 AI:", clr='g')
        else:
            print("\n🤖 AI:")
        
        response_parts = []
        for part in bridge.chat_stream(prompt, model='claude-sonnet-4'):
            if part and isinstance(part, str):
                print(part, end='', flush=True)
                response_parts.append(part)
        
        print("\n")  # Add newline after streaming response
        return ''.join(response_parts)
        
    except Exception as e:
        if HAS_CLRPRINT:
            clrprint(f"❌ Error: {e}", clr='r')
        else:
            print(f"❌ Error: {e}")
        return None

def main():
    """Main chat loop"""
    print_welcome()
    
    if HAS_CLRPRINT:
        clrprint("\n🚀 Starting bridge...", clr='y')
    else:
        print("\n🚀 Starting bridge...")
    
    bridge = a_bridge()
    
    try:
        # Initialize the bridge
        bridge.run()
        time.sleep(2)
        
        # Test connection
        test_result = bridge.send_js("1 + 1")
        if test_result != 2:
            if HAS_CLRPRINT:
                clrprint(f"⚠️  Bridge test failed. Expected 2, got {test_result}", clr='r')
            else:
                print(f"⚠️  Bridge test failed. Expected 2, got {test_result}")
            return
        
        if HAS_CLRPRINT:
            clrprint("✅ Bridge connected successfully!\n", clr='g')
        else:
            print("✅ Bridge connected successfully!\n")
        
        # Chat loop
        conversation_history = []
        
        while True:
            try:
                # Get user input
                if HAS_CLRPRINT:
                    user_input = clrinput("👤 You: ", clr='c').strip()
                else:
                    user_input = input("👤 You: ").strip()
                
                # Check for exit command
                if user_input.lower() in ['exit', 'quit', 'bye']:
                    if HAS_CLRPRINT:
                        clrprint("\n👋 Goodbye!", clr='y')
                    else:
                        print("\n👋 Goodbye!")
                    break
                
                # Skip empty input
                if not user_input:
                    continue
                
                # Add user message to conversation history
                conversation_history.append({
                    'role': 'user',
                    'content': user_input
                })
                
                # Get and print streaming response
                ai_response = print_streaming_response(bridge, user_input)
                
                if ai_response:
                    # Add AI response to conversation history
                    conversation_history.append({
                        'role': 'assistant',
                        'content': ai_response
                    })
                
            except KeyboardInterrupt:
                if HAS_CLRPRINT:
                    clrprint("\n\n👋 Chat interrupted. Goodbye!", clr='y')
                else:
                    print("\n\n👋 Chat interrupted. Goodbye!")
                break
            except Exception as e:
                if HAS_CLRPRINT:
                    clrprint(f"❌ Unexpected error: {e}", clr='r')
                else:
                    print(f"❌ Unexpected error: {e}")
                continue
        
    except Exception as e:
        if HAS_CLRPRINT:
            clrprint(f"❌ Failed to start bridge: {e}", clr='r')
        else:
            print(f"❌ Failed to start bridge: {e}")
    finally:
        if HAS_CLRPRINT:
            clrprint("🔌 Closing bridge...", clr='y')
        else:
            print("🔌 Closing bridge...")
        bridge.close()

if __name__ == "__main__":
    main()
