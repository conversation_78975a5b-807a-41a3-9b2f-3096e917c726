#!/usr/bin/env python3
"""
Debug script to test Puter AI response format
"""

from puter import a_bridge
import time
import json

def main():
    print("=== Debug Puter AI Response Format ===")
    
    bridge = a_bridge()
    
    try:
        # Initialize the bridge
        print("Starting bridge...")
        bridge.run()
        time.sleep(2)
        
        # Test connection
        test_result = bridge.send_js("1 + 1")
        print(f"Bridge test: {test_result}")
        
        if test_result != 2:
            print("Bridge test failed!")
            return
        
        print("Bridge connected successfully!")
        
        # Test raw Puter AI call
        print("\n=== Testing raw Puter AI call ===")
        raw_js = """
        (async () => {
            try {
                const response = await puter.ai.chat('Hello, how are you?', {model: 'claude-sonnet-4'});
                console.log('Raw response:', response);
                return {
                    type: typeof response,
                    response: response,
                    keys: Object.keys(response || {}),
                    message: response?.message,
                    content: response?.message?.content,
                    text: response?.text
                };
            } catch (error) {
                return { error: error.message };
            }
        })()
        """
        
        raw_result = bridge.send_js(raw_js, timeout=30)
        print(f"Raw result: {json.dumps(raw_result, indent=2)}")
        
        # Test streaming call
        print("\n=== Testing streaming call ===")
        stream_js = """
        (async () => {
            try {
                const response = await puter.ai.chat('Say hello', {model: 'claude-sonnet-4', stream: true});
                const parts = [];
                let count = 0;
                for await (const part of response) {
                    count++;
                    parts.push({
                        index: count,
                        type: typeof part,
                        part: part,
                        keys: Object.keys(part || {}),
                        text: part?.text,
                        delta: part?.delta,
                        content: part?.delta?.content
                    });
                    if (count > 5) break; // Limit to first 5 parts for debugging
                }
                return { totalParts: count, sampleParts: parts };
            } catch (error) {
                return { error: error.message };
            }
        })()
        """
        
        stream_result = bridge.send_js(stream_js, timeout=30)
        print(f"Stream result: {json.dumps(stream_result, indent=2)}")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        print("Closing bridge...")
        bridge.close()

if __name__ == "__main__":
    main()
