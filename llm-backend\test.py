#!/usr/bin/env python3
from puter import a_bridge
import time

from clrprint import clrinput
from clrprint import clrprint

def main():
    clrprint("=== starting bridge ===", clr='y')
    
    bridge = a_bridge()
    
    try:
        bridge.run()
        time.sleep(2)
        
        print("\ntesting 1+2")
        result = bridge.send_js("1 + 2")
        
        if result == 3:
            clrprint("✓ server healthy, got 3", clr='g')
        else:
            clrprint(f"✗ server unhealthy - expected 3, got {result}", clr='r')
        
        clrprint("\n=== server test complete ===", clr='b')
        input("[enter] to exit")
        
    except Exception as e:
        print(f"oops: {e}")
    finally:
        bridge.close()

if __name__ == "__main__":
    main()
