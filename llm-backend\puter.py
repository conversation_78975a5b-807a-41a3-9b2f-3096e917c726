import time
import asyncio
import websockets
import json
import threading

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # suppress logs to only fatal

import warnings
warnings.filterwarnings("ignore")


class a_bridge:
    def __init__(self, port=8765):
        self.port = port
        self.driver = None
        self.connected_clients = set()
        self.message_responses = {}
        self.message_id_counter = 0
        self.server_thread = None

    def setup_browser(self):
        """Setup Chrome browser"""
        chrome_options = Options()
        chrome_options.add_argument("--log-level=3")
        self.driver = webdriver.Chrome(options=chrome_options)

    async def websocket_handler(self, websocket, _path):
        """Handle WebSocket connections"""
        print(f"Browser connected")
        self.connected_clients.add(websocket)

        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get('type') == 'js_result':
                    message_id = data.get('id')
                    if message_id is not None:
                        self.message_responses[message_id] = data
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            self.connected_clients.discard(websocket)

    def start_server(self):
        """Start WebSocket server"""
        def run_server():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            start_server = websockets.serve(self.websocket_handler, "localhost", self.port)
            loop.run_until_complete(start_server)
            loop.run_forever()

        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()

    def send_js(self, code, timeout=5):
        """Send JavaScript code and get result"""
        if not self.connected_clients:
            return None

        message_id = self.message_id_counter
        self.message_id_counter += 1

        message = {'type': 'execute_js', 'code': code, 'id': message_id}

        # Send to browser
        for client in self.connected_clients.copy():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(client.send(json.dumps(message)))
            except:
                self.connected_clients.discard(client)

        # Wait for response
        start_time = time.time()
        while time.time() - start_time < timeout:
            if message_id in self.message_responses:
                response = self.message_responses.pop(message_id)
                if response.get('success'):
                    return response.get('result')
                else:
                    return f"Error: {response.get('error')}"
            time.sleep(0.1)

        return "Timeout"

    def run(self):
        """Setup and run the bridge"""
        print("Starting WebSocket server...")
        self.start_server()
        time.sleep(1)

        print("Setting up browser...")
        self.setup_browser()

        print("Navigating to Puter playground...")
        self.driver.get("https://docs.puter.com/playground/")
        time.sleep(2)

        print("Injecting script...")
        # Read the simple HTML file
        with open('bridge.html', 'r') as f:
            html_content = f.read()

        # Inject it properly by setting the editor value
        self.driver.execute_script(f"editor.setValue({json.dumps(html_content)});")
        time.sleep(0.5)

        print("Running script...")
        run_button = self.driver.find_element(By.ID, "run")
        run_button.click()
        time.sleep(2)

        return self

    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()


def main():
    bridge = a_bridge()
    try:
        bridge.run()
        input("[enter] to exit")
    finally:
        bridge.close()


if __name__ == "__main__":
    main()

