import time
import asyncio
import websockets
import json
import threading

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options

import os
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'  # suppress logs to only fatal

import warnings
warnings.filterwarnings("ignore")


class a_bridge:
    def __init__(self, port=8765):
        self.port = port
        self.driver = None
        self.connected_clients = set()
        self.message_responses = {}
        self.message_id_counter = 0
        self.server_thread = None

    def setup_browser(self):
        """Setup Chrome browser"""
        chrome_options = Options()
        chrome_options.add_argument("--log-level=3")
        self.driver = webdriver.Chrome(options=chrome_options)

    async def websocket_handler(self, websocket, _path):
        """Handle WebSocket connections"""
        print(f"Browser connected")
        self.connected_clients.add(websocket)

        try:
            async for message in websocket:
                data = json.loads(message)
                if data.get('type') == 'js_result':
                    message_id = data.get('id')
                    if message_id is not None:
                        self.message_responses[message_id] = data
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            self.connected_clients.discard(websocket)

    def start_server(self):
        """Start WebSocket server"""
        def run_server():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            start_server = websockets.serve(self.websocket_handler, "localhost", self.port)
            loop.run_until_complete(start_server)
            loop.run_forever()

        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()

    def send_js(self, code, timeout=5):
        """Send JavaScript code and get result"""
        if not self.connected_clients:
            return None

        message_id = self.message_id_counter
        self.message_id_counter += 1

        message = {'type': 'execute_js', 'code': code, 'id': message_id}

        # Send to browser
        for client in self.connected_clients.copy():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(client.send(json.dumps(message)))
            except:
                self.connected_clients.discard(client)

        # Wait for response
        start_time = time.time()
        while time.time() - start_time < timeout:
            if message_id in self.message_responses:
                response = self.message_responses.pop(message_id)
                if response.get('success'):
                    return response.get('result')
                else:
                    return f"Error: {response.get('error')}"
            time.sleep(0.1)

        return "Timeout"

    def run(self):
        """Setup and run the bridge"""
        print("Starting WebSocket server...")
        self.start_server()
        time.sleep(1)

        print("Setting up browser...")
        self.setup_browser()

        print("Navigating to Puter playground...")
        self.driver.get("https://docs.puter.com/playground/")
        time.sleep(2)

        print("Injecting script...")
        # Read the simple HTML file
        with open('bridge.html', 'r') as f:
            html_content = f.read()

        # Inject it properly by setting the editor value
        self.driver.execute_script(f"editor.setValue({json.dumps(html_content)});")
        time.sleep(0.5)

        print("Running script...")
        run_button = self.driver.find_element(By.ID, "run")
        run_button.click()
        time.sleep(2)

        return self

    def chat(self, prompt, model='claude-sonnet-4', stream=False, max_tokens=None, temperature=None):
        """
        Send a chat prompt to Puter AI

        Args:
            prompt (str): The prompt to send
            model (str): AI model to use (default: claude-sonnet-4)
            stream (bool): Whether to stream the response (default: False)
            max_tokens (int): Maximum tokens to generate
            temperature (float): Temperature for randomness (0-2)

        Returns:
            str or async iterator: Response text or streaming iterator
        """
        options = {'model': model}
        if stream:
            options['stream'] = True
        if max_tokens is not None:
            options['max_tokens'] = max_tokens
        if temperature is not None:
            options['temperature'] = temperature

        js_code = f"""
        (async () => {{
            try {{
                const response = await puter.ai.chat({json.dumps(prompt)}, {json.dumps(options)});
                // For non-streaming responses, return the message content
                if (response && response.message) {{
                    return response.message.content || response.message;
                }}
                // If it's a string response, return it directly
                if (typeof response === 'string') {{
                    return response;
                }}
                return response;
            }} catch (error) {{
                return {{ error: error.message }};
            }}
        }})()
        """

        return self.send_js(js_code, timeout=30)

    def chat_stream(self, prompt, model='claude-sonnet-4', max_tokens=None, temperature=None):
        """
        Send a chat prompt to Puter AI with streaming response

        Args:
            prompt (str): The prompt to send
            model (str): AI model to use (default: claude-sonnet-4)
            max_tokens (int): Maximum tokens to generate
            temperature (float): Temperature for randomness (0-2)

        Returns:
            generator: Yields response parts as they arrive
        """
        options = {'model': model, 'stream': True}
        if max_tokens is not None:
            options['max_tokens'] = max_tokens
        if temperature is not None:
            options['temperature'] = temperature

        js_code = f"""
        (async () => {{
            try {{
                const response = await puter.ai.chat({json.dumps(prompt)}, {json.dumps(options)});
                const parts = [];
                for await (const part of response) {{
                    if (part && part.text) {{
                        parts.push(part.text);
                    }} else if (part && part.delta && part.delta.content) {{
                        parts.push(part.delta.content);
                    }} else if (typeof part === 'string') {{
                        parts.push(part);
                    }}
                }}
                return parts;
            }} catch (error) {{
                return {{ error: error.message }};
            }}
        }})()
        """

        result = self.send_js(js_code, timeout=60)

        if isinstance(result, list):
            for part in result:
                if part:  # Only yield non-empty parts
                    yield part
        elif isinstance(result, dict) and 'error' in result:
            yield f"Error: {result['error']}"
        elif isinstance(result, str) and result.startswith('Error:'):
            yield result
        else:
            yield str(result)

    def chat_messages(self, messages, model='claude-sonnet-4', stream=False, max_tokens=None, temperature=None):
        """
        Send a message array to Puter AI

        Args:
            messages (list): Array of message objects with 'role' and 'content'
            model (str): AI model to use (default: claude-sonnet-4)
            stream (bool): Whether to stream the response (default: False)
            max_tokens (int): Maximum tokens to generate
            temperature (float): Temperature for randomness (0-2)

        Returns:
            str or async iterator: Response text or streaming iterator
        """
        options = {'model': model}
        if stream:
            options['stream'] = True
        if max_tokens is not None:
            options['max_tokens'] = max_tokens
        if temperature is not None:
            options['temperature'] = temperature

        js_code = f"""
        (async () => {{
            try {{
                const response = await puter.ai.chat({json.dumps(messages)}, {json.dumps(options)});
                // For non-streaming responses, return the message content
                if (response && response.message) {{
                    return response.message.content || response.message;
                }}
                // If it's a string response, return it directly
                if (typeof response === 'string') {{
                    return response;
                }}
                return response;
            }} catch (error) {{
                return {{ error: error.message }};
            }}
        }})()
        """

        return self.send_js(js_code, timeout=30)

    def close(self):
        """Close the browser"""
        if self.driver:
            self.driver.quit()


def main():
    bridge = a_bridge()
    try:
        bridge.run()
        input("[enter] to exit")
    finally:
        bridge.close()


if __name__ == "__main__":
    main()

